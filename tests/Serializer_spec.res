open Jest
open Fixtures
open Accounting__Types

module Serializer = Accounting__Serializer

describe("Serializer", () => {
  open Expect
  open Serializer

  describe("with decimalPrecision set to 2", () => {
    let productInputA = ProductInputs.make(
      ProductInputs.quantity_10_unit_price_10_01_tax_20,
      ~discounts=[DiscountInputs.percent_20],
      ~fees=[FeeInputs.other_6_66, FeeInputs.tax_0_09, FeeInputs.transport_32_01],
      (),
    )
    let productInputB = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_1_33_tax_20,
      ~discounts=[DiscountInputs.free_0],
      ~fees=[FeeInputs.tax_0_09, FeeInputs.transport_1_09],
      (),
    )
    let productInputC = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_0_19_tax_2_1,
      ~fees=[FeeInputs.tax_3_33],
      (),
    )
    let productInputD = ProductInputs.make(
      ProductInputs.quantity_0_unit_price_20_1281_tax_20,
      ~discounts=[DiscountInputs.currency_6_66],
      (),
    )

    let cart =
      {
        products: [productInputA, productInputB, productInputC, productInputD],
        discounts: [],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make
      ->Formatter.Cart.make

    let cartSerialized = cart->serialize
    let cartDeserialized = cartSerialized->deserialize

    describe(
      "Currency",
      () => {
        test(
          "should serialize Eur currency input",
          () => toBe("eur", expect(Eur->Currency.serialize)),
        )
        test(
          "should serialize Usd currency input",
          () => toBe("usd", expect(Usd->Currency.serialize)),
        )
        test(
          "should deserialize Usd currency input",
          () => toBe(Usd, expect(Usd->Currency.serialize->Currency.deserialize)),
        )
        test(
          "should deserialize Usd currency input",
          () => toBe(Eur, expect(Eur->Currency.serialize->Currency.deserialize)),
        )
      },
    )

    describe(
      "Tax",
      () => {
        let serializedTax: SerializedTypes.tax = {rate: "20"}
        let deserializedTax: tax = {
          rate: 20.,
          amount: None,
          formattedAmount: None,
        }->Formatter.ProductTax.make(
          ~currency=cart.currency,
          ~precision=Some(cart.decimalPrecision),
        )

        test(
          "should deserialize input to retrieve productTax",
          () =>
            toEqual(
              deserializedTax,
              expect(serializedTax->Tax.deserialize(~decimalPrecision=cart.decimalPrecision)),
            ),
        )

        test(
          "should deserialize input to retrieve productTax",
          () => toEqual(serializedTax, expect(deserializedTax->Tax.serialize)),
        )
      },
    )

    describe(
      "Discount",
      () => {
        test(
          "should serialize Currency discount kind",
          () => toBe("currency", expect(Currency->Discount.serializeKind)),
        )
        test(
          "should serialize Percent discount kind",
          () => toBe("percent", expect(Percent->Discount.serializeKind)),
        )
        test(
          "should serialize Free discount kind",
          () => toBe("free", expect(Free->Discount.serializeKind)),
        )

        test(
          "should deserialize to retrieve Currency discount kind",
          () => toBe(Currency, expect("currency"->Discount.deserializeKind)),
        )
        test(
          "should deserialize to retrieve Percent discount kind",
          () => toBe(Percent, expect("percent"->Discount.deserializeKind)),
        )
        test(
          "should deserialize to retrieve Free discount kind",
          () => toBe(Free, expect("free"->Discount.deserializeKind)),
        )

        test(
          "should deserialize a serialized discount",
          () => {
            let serializedDiscount: SerializedTypes.discount = {
              id: "123",
              name: "Fidelity",
              kind: "free",
              value: "0",
              quantity: 10,
            }
            let deserializedDiscount: discountInput = {
              id: Some("123"),
              name: "Fidelity",
              kind: Free,
              value: 0.,
              quantity: 10,
            }

            toEqual(deserializedDiscount, expect(serializedDiscount->Discount.deserialize))
          },
        )

        test(
          "should serialize a discount",
          () => {
            let deserializedDiscount: discount = {
              id: "123",
              name: "Fidelity",
              kind: Currency,
              value: 10.,
              formattedValue: Some("0"),
              quantity: 0.->Big.fromFloat,
              amount: Some(10.->Big.fromFloat),
              formattedAmount: Some("10"),
              warnings: [],
            }
            let serializedDiscount: SerializedTypes.discount = {
              id: "123",
              name: "Fidelity",
              kind: "currency",
              value: "10",
              quantity: 0,
            }

            toEqual(serializedDiscount, expect(deserializedDiscount->Discount.serialize))
          },
        )
      },
    )

    describe(
      "Fee",
      () => {
        test(
          "should serialize Transport fee kind",
          () => toBe("transport", expect(Transport->Fee.serializeKind)),
        )
        test(
          "should serialize Taxes fee kind",
          () => toBe("taxes", expect(Taxes->Fee.serializeKind)),
        )
        test(
          "should serialize Other fee kind",
          () => toBe("other", expect(Other->Fee.serializeKind)),
        )

        test(
          "should deserialize Transport fee kind",
          () => toBe(Transport, expect(Transport->Fee.serializeKind->Fee.deserializeKind)),
        )
        test(
          "should deserialize Taxes fee kind",
          () => toBe(Taxes, expect(Taxes->Fee.serializeKind->Fee.deserializeKind)),
        )
        test(
          "should deserialize Other fee kind",
          () => {
            let expectedResult: feeKind = Other

            toBe(expectedResult, expect(Other->Fee.serializeKind->Fee.deserializeKind))
          },
        )

        test(
          "should deserialize a serialized fee",
          () => {
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "other",
              amount: "13.23",
            }
            let feeInput: feeInput = {
              id: Some("123"),
              kind: Other,
              amount: 13.23,
            }

            toEqual(feeInput, expect(serializedFee->Fee.deserialize))
          },
        )
        test(
          "should serialize a fee",
          () => {
            let deserializedFee: fee = {
              id: "123",
              kind: Taxes,
              amount: 9.12,
              formattedAmount: Some("9.12"),
              totalAmount: None,
              formattedTotalAmount: None,
            }
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "taxes",
              amount: "9.12",
            }

            toEqual(serializedFee, expect(deserializedFee->Fee.serialize))
          },
        )
      },
    )

    describe(
      "Cart",
      () => {
        test("should correctly serialise input", () => expect(cartSerialized)->toMatchSnapshot)

        test(
          "should output correct currency value after deserialization",
          () => toBe(Eur, expect(cartDeserialized.currency)),
        )

        test(
          "should have a product with correct fee kind value after deserialization",
          () => {
            let identifier = switch productInputC {
            | Unit({product: {identifier}}) | Bulk({product: {identifier}, _}) => identifier
            }
            let fees = switch cartDeserialized.products->Utils.getProductByKey(identifier) {
            | Unit({fees}) | Bulk({fees}, _) => fees
            }

            toBe(Taxes, expect((fees->Array.getExn(0)).kind))
          },
        )

        test(
          "should have a product with correct discount kind value after deserialization",
          () => {
            let stockKeepingUnit = switch productInputA {
            | Unit({product: {stockKeepingUnit}})
            | Bulk({product: {stockKeepingUnit}, _}) => stockKeepingUnit
            }
            let discounts = switch cartDeserialized.products->Utils.getProductByKey(
              stockKeepingUnit,
            ) {
            | Unit({discounts}) | Bulk({discounts}, _) => discounts
            }

            toBe(Percent, expect((discounts->Array.getExn(0)).kind))
          },
        )

        test("should correctly deserialise input", () => expect(cartDeserialized)->toMatchSnapshot)
      },
    )
  })

  describe("with decimalPrecision set to 3", () => {
    let productInputA = ProductInputs.make(
      ProductInputs.quantity_301_unit_price_19_881_tax_20,
      ~discounts=[DiscountInputs.currency_6_66],
      ~fees=[FeeInputs.other_6_66, FeeInputs.transport_32_01],
      (),
    )
    let productInputB = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_1_33_tax_20,
      ~discounts=[DiscountInputs.free_0],
      ~fees=[FeeInputs.tax_0_09, FeeInputs.transport_1_09],
      (),
    )
    let productInputC = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_0_19_tax_2_1,
      ~fees=[FeeInputs.tax_3_33],
      (),
    )
    let bulkProductInputD = ProductInputs.make(
      ProductInputs.bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20,
      ~discounts=[DiscountInputs.percent_20],
      (),
    )

    let cart =
      {
        products: [productInputA, productInputB, productInputC, bulkProductInputD],
        discounts: [],
        decimalPrecision: 3,
        currency: Usd,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make
      ->Formatter.Cart.make

    let cartSerialized = cart->serialize
    let cartDeserialized = cartSerialized->deserialize

    describe(
      "Currency",
      () => {
        test(
          "should serialize Eur currency input",
          () => toBe("eur", expect(Eur->Currency.serialize)),
        )
        test(
          "should serialize Usd currency input",
          () => toBe("usd", expect(Usd->Currency.serialize)),
        )
        test(
          "should deserialize Usd currency input",
          () => toBe(Usd, expect(Usd->Currency.serialize->Currency.deserialize)),
        )
        test(
          "should deserialize Usd currency input",
          () => toBe(Eur, expect(Eur->Currency.serialize->Currency.deserialize)),
        )
      },
    )

    describe(
      "Tax",
      () => {
        let serializedTax: SerializedTypes.tax = {rate: "20"}
        let deserializedTax: tax = {
          rate: 20.,
          amount: None,
          formattedAmount: None,
        }->Formatter.ProductTax.make(
          ~currency=cart.currency,
          ~precision=Some(cart.decimalPrecision),
        )

        test(
          "should deserialize input to retrieve productTax",
          () =>
            toEqual(
              deserializedTax,
              expect(serializedTax->Tax.deserialize(~decimalPrecision=cart.decimalPrecision)),
            ),
        )

        test(
          "should deserialize input to retrieve productTax",
          () => toEqual(serializedTax, expect(deserializedTax->Tax.serialize)),
        )
      },
    )

    describe(
      "Discount",
      () => {
        test(
          "should serialize Currency discount kind",
          () => toBe("currency", expect(Currency->Discount.serializeKind)),
        )
        test(
          "should serialize Percent discount kind",
          () => toBe("percent", expect(Percent->Discount.serializeKind)),
        )
        test(
          "should serialize Free discount kind",
          () => toBe("free", expect(Free->Discount.serializeKind)),
        )

        test(
          "should deserialize to retrieve Currency discount kind",
          () => toBe(Currency, expect("currency"->Discount.deserializeKind)),
        )
        test(
          "should deserialize to retrieve Percent discount kind",
          () => toBe(Percent, expect("percent"->Discount.deserializeKind)),
        )
        test(
          "should deserialize to retrieve Free discount kind",
          () => toBe(Free, expect("free"->Discount.deserializeKind)),
        )

        test(
          "should deserialize a serialized discount",
          () => {
            let serializedDiscount: SerializedTypes.discount = {
              id: "123",
              name: "Fidelity",
              kind: "free",
              value: "0",
              quantity: 10,
            }

            toEqual(
              {
                id: Some("123"),
                name: "Fidelity",
                kind: Free,
                value: 0.,
                quantity: 10,
              },
              expect(serializedDiscount->Discount.deserialize),
            )
          },
        )

        test(
          "should serialize a discount",
          () => {
            let deserializedDiscount: discount = {
              id: "123",
              name: "Fidelity",
              kind: Currency,
              value: 10.,
              formattedValue: Some("0"),
              quantity: 10.->Big.fromFloat,
              amount: Some(10.->Big.fromFloat),
              formattedAmount: Some("10"),
              warnings: [],
            }

            toEqual(
              {
                SerializedTypes.id: "123",
                name: "Fidelity",
                kind: "currency",
                value: "10",
                quantity: 10,
              },
              expect(deserializedDiscount->Discount.serialize),
            )->ignore

            toEqual(
              {
                SerializedTypes.id: "123",
                name: "Fidelity",
                kind: "currency",
                value: "10",
                quantity: 10000,
              },
              expect(deserializedDiscount->Discount.serialize(~capacityPrecision=3)),
            )
          },
        )
      },
    )

    describe(
      "Fee",
      () => {
        test(
          "should serialize Transport fee kind",
          () => toBe("transport", expect(Transport->Fee.serializeKind)),
        )
        test(
          "should serialize Taxes fee kind",
          () => toBe("taxes", expect(Taxes->Fee.serializeKind)),
        )
        test(
          "should serialize Other fee kind",
          () => toBe("other", expect(Other->Fee.serializeKind)),
        )

        test(
          "should deserialize Transport fee kind",
          () => toBe(Transport, expect(Transport->Fee.serializeKind->Fee.deserializeKind)),
        )
        test(
          "should deserialize Taxes fee kind",
          () => toBe(Taxes, expect(Taxes->Fee.serializeKind->Fee.deserializeKind)),
        )
        test(
          "should deserialize Other fee kind",
          () => {
            let expectedResult: feeKind = Other

            toBe(expectedResult, expect(Other->Fee.serializeKind->Fee.deserializeKind))
          },
        )

        test(
          "should deserialize a serialized fee",
          () => {
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "other",
              amount: "13.23",
            }
            let feeInput: feeInput = {
              id: Some("123"),
              kind: Other,
              amount: 13.23,
            }

            toEqual(feeInput, expect(serializedFee->Fee.deserialize))
          },
        )
        test(
          "should serialize a fee",
          () => {
            let deserializedFee: fee = {
              id: "123",
              kind: Taxes,
              amount: 9.12,
              formattedAmount: Some("9.12"),
              totalAmount: None,
              formattedTotalAmount: None,
            }
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "taxes",
              amount: "9.12",
            }

            toEqual(serializedFee, expect(deserializedFee->Fee.serialize))
          },
        )
      },
    )

    describe(
      "Cart",
      () => {
        test("should correctly serialise input", () => expect(cartSerialized)->toMatchSnapshot)

        test(
          "should output correct currency value after deserialization",
          () => toBe(Usd, expect(cartDeserialized.currency)),
        )

        test(
          "should have a product with correct fee kind value after deserialization",
          () => {
            let identifier = switch productInputC {
            | Unit({product: {identifier}}) | Bulk({product: {identifier}, _}) => identifier
            }
            let fees = switch cartDeserialized.products->Utils.getProductByKey(identifier) {
            | Unit({fees}) | Bulk({fees}, _) => fees
            }

            toBe(Taxes, expect((fees->Array.getExn(0)).kind))
          },
        )

        test(
          "should have a product with correct discount kind value after deserialization",
          () => {
            let stockKeepingUnit = switch productInputA {
            | Unit({product: {stockKeepingUnit}})
            | Bulk({product: {stockKeepingUnit}, _}) => stockKeepingUnit
            }
            let discounts = switch cartDeserialized.products->Utils.getProductByKey(
              stockKeepingUnit,
            ) {
            | Unit({discounts}) | Bulk({discounts}, _) => discounts
            }

            toBe(Currency, expect((discounts->Array.getExn(0)).kind))
          },
        )

        test("should correctly deserialise input", () => expect(cartDeserialized)->toMatchSnapshot)
      },
    )
  })

  describe("cart normalization", () => {
    describe(
      "with a serialized cart which doesn't have decimalPrecision",
      () => {
        let serializedCartWithoutDecimalPrecision = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3,
              "packaging": 7,
              "expectedQuantity": 10,
              "quantity": 10,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "bulk": true,
              "capacityPrecision": 0
            }
          ],
          "discounts": [],
          "currency": "eur",
          "taxesIncluded": false,
          "taxesFree": false
        }
      `

        test(
          "cart should have decimalPrecision after deserialization",
          () =>
            toBe(5, expect((serializedCartWithoutDecimalPrecision->deserialize).decimalPrecision)),
        )
      },
    )

    describe(
      "with a serialized cart which have wrong bulk value",
      () => {
        let serializedCartWithWrongBulkValue = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3,
              "packaging": 7,
              "expectedQuantity": 10,
              "quantity": 10,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "bulk": {
                "wrongBulkField": true,
                "anotherWrongField": 1000
              },
              "capacityPrecision": 0
            }
          ],
          "discounts": [],
          "currency": "eur",
          "taxesIncluded": false,
          "taxesFree": false
        }
      `

        test(
          "cart should have falsy bulk after deserialization",
          () => expect(serializedCartWithWrongBulkValue->deserialize)->toMatchSnapshot,
        )
      },
    )

    describe(
      "with a serialized cart which doesn't have bulk field",
      () => {
        let serializedCartWithoutBulkField = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3,
              "packaging": 7,
              "expectedQuantity": 10,
              "quantity": 10,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "capacityPrecision": 0
            }
          ],
          "discounts": [],
          "currency": "eur",
          "taxesIncluded": false,
          "taxesFree": false
        }
      `

        test(
          "bulk field should be added and have a falsy value after deserialization",
          () => expect(serializedCartWithoutBulkField->deserialize)->toMatchSnapshot,
        )
      },
    )

    test(
      "should match snapshot",
      () => {
        let serializedCart = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3500,
              "packaging": 2000,
              "expectedQuantity": 10000,
              "quantity": 10000,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "capacityPrecision": 3,
              "bulk": true
            }
          ],
          "discounts": [],
          "currency": "eur"
        }
      `

        expect(serializedCart->deserialize)->toMatchSnapshot
      },
    )
  })
})
