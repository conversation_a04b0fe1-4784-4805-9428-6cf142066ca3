open Jest
open Fixtures
open Accounting__Types
open Accounting__Exception

module Validator = Accounting__Validator
module Maker = Accounting__Maker
module Computer = Accounting__Computer

let shouldReturnError = "Should return an error here!"
let shouldReturnOk = "Should be ok here!"

describe("Validation", () => {
  open Expect
  open Validator

  // We use this just to have an instance of a cart
  // (using its #decimalPrecision later in computes)
  let initialCart = {
    products: [],
    discounts: [],
    decimalPrecision: 2,
    currency: Eur,
    taxesFree: false,
    standardTaxRate: 20.,
  }->Maker.Cart.make

  describe("Fee", () =>
    describe(
      "validate",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not validate fee input as a good one",
              () => {
                let validationResult = FeeInputs.other_error_negative_10_01->Fee.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not validate fee as a good one",
              () => {
                let validationResult = Fees.other_error_negative_10_01->Fee.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )
          },
        )

        describe(
          "without errors",
          () => {
            test(
              "should validate fee input as a good one",
              () => {
                let validationResult = FeeInputs.other_6_66->Fee.validateInput()

                switch validationResult {
                | Error(_) => fail(shouldReturnOk)
                | Ok() => toMatchSnapshot(expect(validationResult))
                }
              },
            )

            test(
              "should validate fee as a good one",
              () => {
                let validationResult = Fees.other_6_66->Fee.validate()

                switch validationResult {
                | Error(_) => fail(shouldReturnOk)
                | Ok() => toMatchSnapshot(expect(validationResult))
                }
              },
            )
          },
        )
      },
    )
  )

  describe("Discount", () => {
    describe(
      "localDiscountCanBeAddedTo",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not be able to add local discount to the product - same fee kind",
              () => {
                let product = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~discounts=[DiscountInputs.free_3],
                    (),
                  )
                }->Maker.Product.make
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                expect(
                  DiscountInputs.free_0
                  ->Maker.Discount.make(~capacityPrecision?)
                  ->Discount.localDiscountCanBeAddedTo(~product),
                ) |> toBe(false)
              },
            )

            test(
              "should not be able to add local discount to the product - quantity is greater than product's one",
              () =>
                toBe(
                  false,
                  expect(
                    Discounts.free_3->Discount.localDiscountCanBeAddedTo(
                      ~product=Products.quantity_1_unit_price_0_09_tax_5_5,
                    ),
                  ),
                ),
            )

            test(
              "should not be able to add local discount to the product - quantity is negative",
              () =>
                toBe(
                  false,
                  expect(
                    Discounts.free_error_negative_7->Discount.localDiscountCanBeAddedTo(
                      ~product=Products.quantity_1_unit_price_0_09_tax_5_5,
                    ),
                  ),
                ),
            )

            test(
              "should not be able to add local discount to the product - currency value is negative",
              () =>
                toBe(
                  false,
                  expect(
                    Discounts.currency_error_negative_0_99->Discount.localDiscountCanBeAddedTo(
                      ~product=Products.quantity_1_unit_price_0_09_tax_5_5,
                    ),
                  ),
                ),
            )

            test(
              "should not be able to add local discount to the product - percent value is negative",
              () =>
                toBe(
                  false,
                  expect(
                    Discounts.percent_error_negative_75->Discount.localDiscountCanBeAddedTo(
                      ~product=Products.quantity_1_unit_price_0_09_tax_5_5,
                    ),
                  ),
                ),
            )
          },
        )

        describe(
          "without errors",
          () => {
            test(
              "should be able to add local discount to the product - currency",
              () => {
                let productInput = ProductInputs.quantity_3_unit_price_0_19_tax_2_1
                let cart =
                  {
                    products: [productInput],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make
                let stockKeepingUnit = switch productInput {
                | Unit({product: {stockKeepingUnit}})
                | Bulk({product: {stockKeepingUnit}, _}) => stockKeepingUnit
                }
                let product = cart.products->Utils.getProductByKey(stockKeepingUnit)
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  true,
                  expect(
                    DiscountInputs.currency_0_33
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeAddedTo(~product),
                  ),
                )
              },
            )

            test(
              "should be able to add local discount to the product - free",
              () => {
                let productInput = ProductInputs.quantity_3_unit_price_1_33_tax_20
                let cart =
                  {
                    products: [productInput],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make
                let stockKeepingUnit = switch productInput {
                | Unit({product: {stockKeepingUnit}})
                | Bulk({product: {stockKeepingUnit}, _}) => stockKeepingUnit
                }
                let product = cart.products->Utils.getProductByKey(stockKeepingUnit)
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  true,
                  expect(
                    DiscountInputs.free_3
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeAddedTo(~product),
                  ),
                )
              },
            )
          },
        )
      },
    )

    describe(
      "localDiscountCanBeUpdatedIn",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not be able to update local discount in the product - discount quantity",
              () => {
                let product = {
                  open ProductInputs
                  make(quantity_1_unit_price_0_09_tax_5_5, ~discounts=[DiscountInputs.free_0], ())
                }->Maker.Product.make
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  false,
                  expect(
                    DiscountInputs.free_3
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeUpdatedIn(~product),
                  ),
                )
              },
            )

            test(
              "should not be able to update local discount in the product - product total price",
              () => {
                let product =
                  {
                    open ProductInputs
                    make(
                      quantity_0_unit_price_20_1281_tax_20,
                      ~discounts=[DiscountInputs.free_0],
                      (),
                    )
                  }
                  ->Maker.Product.make
                  ->Computer.ComputerProduct.computeTotalPrice(~cart=initialCart)
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  false,
                  expect(
                    DiscountInputs.currency_12_09
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeUpdatedIn(~product),
                  ),
                )
              },
            )
          },
        )

        describe(
          "without errors",
          () => {
            test(
              "should be able to update local discount in the product - free",
              () => {
                let product = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~discounts=[DiscountInputs.free_3],
                    (),
                  )
                }->Maker.Product.make
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  true,
                  expect(
                    DiscountInputs.free_0
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeUpdatedIn(~product),
                  ),
                )
              },
            )

            test(
              "should be able to update local discount in the product - currency",
              () => {
                let product =
                  {
                    open ProductInputs
                    make(
                      quantity_13_unit_price_20_11_tax_10,
                      ~discounts=[DiscountInputs.free_3],
                      (),
                    )
                  }
                  ->Maker.Product.make
                  ->Computer.ComputerProduct.computeTotalPrice(~cart=initialCart)
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  true,
                  expect(
                    DiscountInputs.currency_12_09
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeUpdatedIn(~product),
                  ),
                )
              },
            )

            test(
              "should be able to update local discount in the product - percent",
              () => {
                let product = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~discounts=[DiscountInputs.currency_0_33],
                    (),
                  )
                }->Maker.Product.make
                let capacityPrecision = switch product {
                | Bulk(_, precision) => Some(precision)
                | Unit(_) => None
                }

                toBe(
                  true,
                  expect(
                    DiscountInputs.percent_3_33
                    ->Maker.Discount.make(~capacityPrecision?)
                    ->Discount.localDiscountCanBeUpdatedIn(~product),
                  ),
                )
              },
            )
          },
        )
      },
    )

    describe(
      "globalDiscountCanBeAddedTo",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not be able to add global discount - no products in cart",
              () => {
                let cart =
                  {
                    products: [],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make

                toBe(
                  false,
                  expect(
                    DiscountInputs.currency_12_09
                    ->Maker.Discount.make
                    ->Discount.globalDiscountCanBeAddedTo(~cart),
                  ),
                )
              },
            )

            test(
              "should not be able to add global discount - discountAmount greater than cartTotalAmount",
              () => {
                let productInput = ProductInputs.quantity_1_unit_price_0_09_tax_5_5
                let cart =
                  {
                    products: [productInput],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make

                toBe(
                  false,
                  expect(
                    DiscountInputs.currency_12_09
                    ->Maker.Discount.make
                    ->Discount.globalDiscountCanBeAddedTo(~cart),
                  ),
                )
              },
            )

            test(
              "should not be able to add local discount to the product - percent value is negative",
              () => {
                let productInput = ProductInputs.quantity_1_unit_price_0_09_tax_5_5
                let cart =
                  {
                    products: [productInput],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make

                toBe(
                  false,
                  expect(
                    Discounts.percent_error_negative_75->Discount.globalDiscountCanBeAddedTo(~cart),
                  ),
                )
              },
            )

            test(
              "should not be able to add local discount to the product - percent value is negative",
              () => {
                let productInput = ProductInputs.quantity_1_unit_price_0_09_tax_5_5
                let cart =
                  {
                    products: [productInput],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make

                toBe(false, expect(Discounts.free_0->Discount.globalDiscountCanBeAddedTo(~cart)))
              },
            )
          },
        )

        describe(
          "without errors",
          () =>
            test(
              "should be able to add global discount",
              () => {
                let productA = ProductInputs.make(
                  ProductInputs.quantity_0_unit_price_20_1281_tax_20,
                  ~fees=[FeeInputs.tax_11_9, FeeInputs.other_6_66],
                  (),
                )
                let productB = ProductInputs.make(
                  ProductInputs.quantity_13_unit_price_20_11_tax_10,
                  ~fees=[FeeInputs.tax_11_9, FeeInputs.other_6_66],
                  (),
                )
                let productC = ProductInputs.make(
                  ProductInputs.quantity_10_unit_price_10_01_tax_20,
                  ~fees=[FeeInputs.tax_11_9, FeeInputs.other_6_66, FeeInputs.transport_32_01],
                  (),
                )
                let cart =
                  {
                    products: [productA, productB, productC],
                    discounts: [],
                    decimalPrecision: 2,
                    currency: Eur,
                    taxesFree: false,
                    standardTaxRate: 20.,
                  }
                  ->Maker.Cart.make
                  ->Computer.make

                toBe(
                  true,
                  expect(
                    DiscountInputs.percent_20
                    ->Maker.Discount.make
                    ->Discount.globalDiscountCanBeAddedTo(~cart),
                  ),
                )
              },
            ),
        )
      },
    )

    describe(
      "validate",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not validate discount as a good one - free and negative value",
              () => {
                let validationResult =
                  DiscountInputs.free_error_negative_7->Discount.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not validate discount as a good one - currency and negative value",
              () => {
                let validationResult =
                  DiscountInputs.currency_error_negative_0_99->Discount.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not validate discount as a good one - percent and incorrect value",
              () => {
                let validationResult = DiscountInputs.percent_error_100_09->Discount.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )
          },
        )

        describe(
          "without errors",
          () => {
            let validationResult = DiscountInputs.percent_3_33->Discount.validateInput()

            test(
              "should validate discount as a good one",
              () =>
                switch validationResult {
                | Error(_) => fail(shouldReturnOk)
                | Ok() => toMatchSnapshot(expect(validationResult))
                },
            )
          },
        )
      },
    )
  })

  describe("Product", () =>
    describe(
      "validateInput",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not be a valid product input - negative quantity",
              () => {
                let validationResult =
                  ProductInputs.error_negative_quantity->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - negative quantity",
              () => {
                let validationResult = Products.error_negative_quantity->Product.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - quantity zero",
              () => {
                let validationResult =
                  Products.quantity_zero->Product.validate(~strategy=Strict, ())

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - discount inside not valid",
              () => {
                let validationResult = {
                  open Products
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~discounts=[Discounts.percent_error_negative_75],
                    (),
                  )
                }->Product.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - fee inside not valid",
              () => {
                let validationResult = {
                  open Products
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~fees=[Fees.transport_error_negative_0_01],
                    (),
                  )
                }->Product.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - negative tax",
              () => {
                let validationResult = ProductInputs.error_negative_tax->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - negative unit price",
              () => {
                let validationResult =
                  ProductInputs.error_negative_unit_price->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - quantity is zero in strict mode",
              () => {
                let validationResult =
                  ProductInputs.quantity_zero->Product.validateInput(~strategy=Strict, ())

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - more than one discount",
              () => {
                let productInput = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_10_01_tax_20,
                    ~discounts=[DiscountInputs.currency_12_09, DiscountInputs.free_3],
                    (),
                  )
                }
                let validationResult = productInput->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - discount inside is not valid",
              () => {
                let productInput = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_10_01_tax_20,
                    ~discounts=[DiscountInputs.free_error_negative_7],
                    (),
                  )
                }
                let validationResult = productInput->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - fee inside is not valid",
              () => {
                let productInput = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~fees=[FeeInputs.tax_error_negative_0_09],
                    (),
                  )
                }
                let validationResult = productInput->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - many fees with same feeKind",
              () => {
                let productInput = {
                  open ProductInputs
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~fees=[FeeInputs.other_0_01, FeeInputs.other_2_03],
                    (),
                  )
                }
                let validationResult = productInput->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - many fees with same feeKind",
              () => {
                let product = {
                  open Products
                  make(
                    quantity_10_unit_price_0_0667_tax_5_5,
                    ~fees=[Fees.other_0_01, Fees.other_2_03],
                    (),
                  )
                }
                let validationResult = product->Product.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - empty name",
              () => {
                let productInput = {
                  open ProductInputs
                  make(error_empty_name, ~fees=[FeeInputs.other_6_66], ())
                }
                let validationResult = productInput->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - empty name",
              () => {
                let validationResult = Products.error_empty_name->Product.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product input - empty description",
              () => {
                let productInput = {
                  open ProductInputs
                  make(error_empty_description, ~fees=[FeeInputs.other_6_66], ())
                }
                let validationResult = productInput->Product.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid product - empty description",
              () => {
                let validationResult = Products.error_empty_description->Product.validate()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )
          },
        )

        describe(
          "without errors",
          () =>
            test(
              "should be a valid product input",
              () => {
                let validationResult =
                  ProductInputs.quantity_3_unit_price_1_33_tax_20->Product.validateInput()

                switch validationResult {
                | Ok() => toMatchSnapshot(expect(validationResult))
                | _ => fail(shouldReturnOk)
                }
              },
            ),
        )
      },
    )
  )

  describe("Cart", () =>
    describe(
      "validateInput",
      () => {
        describe(
          "with errors",
          () => {
            test(
              "should not be a valid cart input - more than one discount",
              () => {
                let cartInput: cartInput = {
                  products: [ProductInputs.quantity_0_unit_price_20_1281_tax_20],
                  discounts: [DiscountInputs.percent_3_33, DiscountInputs.free_3],
                  decimalPrecision: 2,
                  currency: Eur,
                  taxesFree: false,
                  standardTaxRate: 20.,
                }

                let validationResult = cartInput->Cart.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid cart input - error in products",
              () => {
                let cartInput: cartInput = {
                  products: [ProductInputs.error_negative_quantity],
                  discounts: [DiscountInputs.free_3],
                  decimalPrecision: 2,
                  currency: Eur,
                  taxesFree: false,
                  standardTaxRate: 20.,
                }

                let validationResult = cartInput->Cart.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )

            test(
              "should not be a valid cart input - error in discounts",
              () => {
                let cartInput: cartInput = {
                  products: [ProductInputs.quantity_10_unit_price_0_0667_tax_5_5],
                  discounts: [DiscountInputs.currency_error_negative_0_99],
                  decimalPrecision: 2,
                  currency: Eur,
                  taxesFree: false,
                  standardTaxRate: 20.,
                }

                let validationResult = cartInput->Cart.validateInput()

                switch validationResult {
                | Error(NotPermitted(message)) => toMatchSnapshot(expect(message))
                | _ => fail(shouldReturnError)
                }
              },
            )
          },
        )

        describe(
          "without errors",
          () =>
            test(
              "should be a valid cart input",
              () => {
                let cartInput: cartInput = {
                  products: [],
                  discounts: [],
                  decimalPrecision: 2,
                  currency: Eur,
                  taxesFree: false,
                  standardTaxRate: 20.,
                }

                let validationResult = cartInput->Cart.validateInput()

                switch validationResult {
                | Ok() => toMatchSnapshot(expect(validationResult))
                | _ => fail(shouldReturnOk)
                }
              },
            ),
        )
      },
    )
  )
})
