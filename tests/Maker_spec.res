open Jest
open Fixtures
open Accounting__Types
open Accounting__Exception

module Maker = Accounting__Maker
module Computer = Accounting__Computer
module Utils = Accounting__Utils

let shouldRaiseAnErrorMessage = "Should raise an error when trying to make a shape with incorrect input values"

describe("Make", () => {
  open Expect

  describe("makeFee", () => {
    describe(
      "with errors",
      () =>
        test(
          "should raise an error when trying to make an incorrect feeInput",
          () =>
            switch FeeInputs.transport_error_negative_0_01->Maker.Fee.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        ),
    )

    describe(
      "without errors",
      () => {
        let fee = FeeInputs.other_0_01->Maker.Fee.make

        test("should match snapshot fee.id", () => toMatchSnapshot(expect(fee.id)))
        test(
          "should correctly make shape of fee",
          () =>
            toEqual(
              (FeeInputs.other_0_01.kind, FeeInputs.other_0_01.amount, None),
              expect((fee.kind, fee.amount, fee.formattedAmount)),
            ),
        )
        test("should match snapshot", () => toMatchSnapshot(expect(fee)))
      },
    )
  })

  describe("makeDiscount", () => {
    describe(
      "with errors",
      () => {
        test(
          "should raise an error when trying to make an incorrect discount (Percent.value) input",
          () =>
            switch DiscountInputs.percent_error_100_09->Maker.Discount.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
        test(
          "should raise an error when trying to make an incorrect discount (Currency.value) input",
          () =>
            switch DiscountInputs.currency_error_negative_0_99->Maker.Discount.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
        test(
          "should raise an error when trying to make an incorrect discount (Free.quantity) input",
          () =>
            switch DiscountInputs.free_error_negative_7->Maker.Discount.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
      },
    )

    describe(
      "without errors",
      () => {
        let discount = DiscountInputs.percent_3_33->Maker.Discount.make

        test("should match snapshot discount.id", () => toMatchSnapshot(expect(discount.id)))
        test(
          "should correctly make shape of discount",
          () =>
            toEqual(
              (
                DiscountInputs.percent_3_33.name,
                DiscountInputs.percent_3_33.kind,
                DiscountInputs.percent_3_33.value,
                DiscountInputs.percent_3_33.quantity->Utils.fromRawProductQuantity,
                None,
                None,
                None,
              ),
              expect((
                discount.name,
                discount.kind,
                discount.value,
                discount.quantity,
                discount.amount,
                discount.formattedValue,
                discount.formattedAmount,
              )),
            ),
        )
        test("should match snapshot", () => toMatchSnapshot(expect(discount)))
      },
    )
  })

  describe("makeProduct", () => {
    describe(
      "with errors",
      () => {
        test(
          "should raise an error when trying to make an incorrect product (Negative unitPrice) input",
          () =>
            switch ProductInputs.error_negative_unit_price->Maker.Product.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
        test(
          "should raise an error when trying to make an incorrect product (Negative quantity) input",
          () =>
            switch ProductInputs.error_negative_quantity->Maker.Product.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
        test(
          "should raise an error when trying to make an incorrect product (Negative tax) input",
          () =>
            switch ProductInputs.error_negative_tax->Maker.Product.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
        test(
          "should raise an error if product input has more than one discount",
          () => {
            let productInput = {
              open ProductInputs
              make(
                quantity_10_unit_price_10_01_tax_20,
                ~discounts=[DiscountInputs.percent_3_33, DiscountInputs.free_0],
                (),
              )
            }

            switch productInput->Maker.Product.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            }
          },
        )
      },
    )

    describe(
      "without errors",
      () => {
        test(
          "should correctly make shape from a product input",
          () =>
            toMatchSnapshot(
              expect(ProductInputs.quantity_13_unit_price_20_11_tax_10->Maker.Product.make),
            ),
        )
        test(
          "should correctly make shape from a product input - product with discounts",
          () => {
            let productInput = {
              open ProductInputs
              make(
                quantity_10_unit_price_10_01_tax_20,
                ~discounts=[DiscountInputs.currency_12_09],
                (),
              )
            }

            toMatchSnapshot(expect(productInput->Maker.Product.make))
          },
        )
        test(
          "should correctly make shape from a product input - product with fees",
          () => {
            let productInput = {
              open ProductInputs
              make(quantity_10_unit_price_10_01_tax_20, ~fees=[FeeInputs.tax_3_33], ())
            }

            toMatchSnapshot(expect(productInput->Maker.Product.make))
          },
        )
      },
    )
  })

  describe("makeCart", () => {
    let productInputA = {
      open ProductInputs
      make(
        quantity_3_unit_price_0_19_tax_2_1,
        ~discounts=[DiscountInputs.currency_6_66],
        ~fees=[FeeInputs.tax_3_33, FeeInputs.transport_32_01],
        (),
      )
    }

    describe(
      "with errors",
      () => {
        let cartInput = {
          products: [productInputA],
          discounts: [DiscountInputs.percent_20, DiscountInputs.percent_3_33],
          decimalPrecision: 2,
          currency: Eur,
          taxesFree: false,
          standardTaxRate: 20.,
        }

        test(
          "should raise an error if cart input has many discounts",
          () =>
            switch cartInput->Maker.Cart.make {
            | exception NotPermitted(message) => toMatchSnapshot(expect(message))
            | _ => fail(shouldRaiseAnErrorMessage)
            },
        )
      },
    )

    describe(
      "without errors",
      () => {
        let cartInput = {
          products: [productInputA],
          discounts: [DiscountInputs.percent_20],
          decimalPrecision: 2,
          currency: Eur,
          taxesFree: false,
          standardTaxRate: 20.,
        }

        test("should match snapshot of cart input", () => toMatchSnapshot(expect(cartInput)))
        test(
          "should correctly make cart shape and match snapshot",
          () => toMatchSnapshot(expect(cartInput->Maker.Cart.make->Computer.make)),
        )
      },
    )
  })
})

open Expect

describe("makeTransportFeeProratedByPrice", () => {
  test("should make a Transport fee input with prorated amount", () => {
    let fee = Maker.Fee.makeTransportFeeProratedByPrice(
      ~globalFeeAmount=10.,
      ~productQuantity=2.,
      ~totalProductPriceExcludingDiscount=40.,
      ~totalCartPriceExcludingGlobalDiscount=80.,
    )
    toEqual((Transport, 2.5, None), expect((fee.kind, fee.amount, fee.formattedAmount)))
  })

  test("should make a Transport fee input with prorated amount with decimal values", () => {
    let fee = Maker.Fee.makeTransportFeeProratedByPrice(
      ~globalFeeAmount=140.55,
      ~productQuantity=66.4,
      ~totalProductPriceExcludingDiscount=336.6,
      ~totalCartPriceExcludingGlobalDiscount=1940.7,
    )
    toBe(0.3671288182564855, expect(fee.amount))
  })

  test(
    "should make a Transport fee input with prorated amount if the total product price is 0",
    () => {
      let fee = Maker.Fee.makeTransportFeeProratedByPrice(
        ~globalFeeAmount=10.,
        ~productQuantity=5.,
        ~totalProductPriceExcludingDiscount=0.,
        ~totalCartPriceExcludingGlobalDiscount=100.,
      )
      toBe(0., expect(fee.amount))
    },
  )

  test(
    "should make a Transport fee input with prorated amount if the total cart price price is 0",
    () => {
      let fee = Maker.Fee.makeTransportFeeProratedByPrice(
        ~globalFeeAmount=10.,
        ~productQuantity=2.,
        ~totalProductPriceExcludingDiscount=40.,
        ~totalCartPriceExcludingGlobalDiscount=0.,
      )
      toBe(0., expect(fee.amount))
    },
  )

  test(
    "should make a Transport fee input with prorated amount if the total product price is 0",
    () => {
      let fee = Maker.Fee.makeTransportFeeProratedByPrice(
        ~globalFeeAmount=10.,
        ~productQuantity=0.,
        ~totalProductPriceExcludingDiscount=40.,
        ~totalCartPriceExcludingGlobalDiscount=50.,
      )
      toBe(0., expect(fee.amount))
    },
  )
})

describe("makeTransportFeePerUnit", () => {
  test("should make a Transport fee input with prorated amount", () => {
    let fee = Maker.Fee.makeTransportFeePerUnit(~globalFeeAmount=10., ~productsTotalUnits=2.)
    toEqual((Transport, 5., None), expect((fee.kind, fee.amount, fee.formattedAmount)))
  })

  test("should make a Transport fee input with prorated amount with decimal values", () => {
    let fee = Maker.Fee.makeTransportFeePerUnit(~globalFeeAmount=336.6, ~productsTotalUnits=70.8)
    toBe(4.754237288135593, expect(fee.amount))
  })

  test(
    "should make a Transport fee input with prorated amount if the products total units is 0",
    () => {
      let fee = Maker.Fee.makeTransportFeePerUnit(~globalFeeAmount=336.6, ~productsTotalUnits=0.)
      toBe(0., expect(fee.amount))
    },
  )
})
