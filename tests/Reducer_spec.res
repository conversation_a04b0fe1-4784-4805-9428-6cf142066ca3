open Jest
open Expect
open Fixtures
open Accounting__Reducer
open Accounting__Actions

module Utils = Accounting__Utils
module Maker = Accounting__Maker
module Computer = Accounting__Computer

describe("reducer", () => {
  let productInputA = {
    open ProductInputs
    make(
      quantity_10_unit_price_10_01_tax_20,
      ~discounts=[DiscountInputs.percent_20],
      ~fees=[FeeInputs.other_6_66, FeeInputs.tax_0_09, FeeInputs.transport_32_01],
      (),
    )
  }
  let productInputB = {
    open ProductInputs
    make(
      quantity_13_unit_price_20_11_tax_10,
      ~discounts=[DiscountInputs.percent_3_33],
      ~fees=[FeeInputs.transport_32_01],
      (),
    )
  }
  let productInputC = {
    open ProductInputs
    make(quantity_3_unit_price_0_19_tax_2_1, ~fees=[FeeInputs.tax_3_33], ())
  }
  let productInputD = {
    open ProductInputs
    make(quantity_0_unit_price_20_1281_tax_20, ~discounts=[DiscountInputs.currency_6_66], ())
  }
  let productInputE = {
    open ProductInputs
    make(quantity_1_unit_price_0_09_tax_5_5, ~discounts=[], ())
  }

  let initialCart =
    {
      products: [productInputA, productInputC],
      discounts: [DiscountInputs.currency_6_66],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }
    ->Maker.Cart.make
    ->Computer.make
  let (productIdA, productIdentifierA) = switch initialCart.products->Array.getExn(0) {
  | Unit({id, identifier}) | Bulk({id, identifier}, _) => (id, identifier)
  }
  let (productIdC, productIdentifierC) = switch initialCart.products->Array.getExn(1) {
  | Unit({id, identifier}) | Bulk({id, identifier}, _) => (id, identifier)
  }

  describe("ProductAdded", () => {
    let cart = initialCart->reducer(ProductAdded(productInputD))

    test("should add product in cart", () => toHaveLength(3, expect(cart.products)))
    test(
      "could not add a product twice in cart -> it just update its quantity",
      () => toHaveLength(3, expect((cart->reducer(ProductAdded(productInputD))).products)),
    )
    test("should match snapshot", () => toMatchSnapshot(expect(cart.products)))
  })

  describe("BatchProductAdded", () => {
    let cart =
      initialCart->reducer(
        BatchProductAdded([productInputA, productInputB, productInputC, productInputD]),
      )

    test("should add products in cart", () => toHaveLength(4, expect(cart.products)))

    test(
      "should match snapshot on first bulk product added",
      () => toMatchSnapshot(expect(cart.products)),
    )

    test(
      "could not add a product twice in cart -> it just update its quantity",
      () =>
        toHaveLength(
          5,
          expect(
            (
              cart->reducer(
                BatchProductAdded([
                  productInputA,
                  productInputB,
                  productInputC,
                  productInputD,
                  productInputE,
                ]),
              )
            ).products,
          ),
        ),
    )

    test(
      "should match snapshot on second bulk product added",
      () => toMatchSnapshot(expect(cart.products)),
    )
  })

  describe("ProductRemoved", () => {
    let cart = initialCart->reducer(ProductRemoved(productIdA))

    test("should remove product from cart", () => toHaveLength(1, expect(cart.products)))
    test(
      "could not remove a product from cart since it's not in it",
      () =>
        toHaveLength(1, expect((cart->reducer(ProductRemoved("not-found-product-id"))).products)),
    )
    test("should match snapshot", () => toMatchSnapshot(expect(cart.products)))
  })

  describe("ProductExpectedQuantityUpdated", () => {
    let cart = initialCart->reducer(ProductExpectedQuantityUpdated(productIdA, UnitQuantity(3)))

    test(
      "should update expected quantity and quantity",
      () => {
        let (expectedQuantity, quantity) = switch cart.products->Utils.getProductByKey(
          productIdentifierA,
        ) {
        | Unit({expectedQuantity, quantity}) => (expectedQuantity, quantity)
        | Bulk(_, _) => failwith(__LOC__)
        }

        toEqual((3, 3), expect((expectedQuantity, quantity)))
      },
    )

    test(
      "should not update when trying to update expected quantity with negative value",
      () => {
        let cart = cart->reducer(ProductExpectedQuantityUpdated(productIdA, UnitQuantity(-1)))
        let (expectedQuantity, quantity) = switch cart.products->Utils.getProductByKey(
          productIdentifierA,
        ) {
        | Unit({expectedQuantity, quantity}) => (expectedQuantity, quantity)
        | Bulk(_, _) => failwith(__LOC__)
        }

        toEqual((3, 3), expect((expectedQuantity, quantity)))
      },
    )

    test("should match snapshot", () => toMatchSnapshot(expect(cart.products)))
  })

  describe("ProductQuantityUpdated", () => {
    let cart = initialCart->reducer(ProductQuantityUpdated(productIdA, UnitQuantity(3)))

    test(
      "should update only quantity",
      () => {
        let (expectedQuantity, quantity) = switch cart.products->Utils.getProductByKey(
          productIdentifierA,
        ) {
        | Unit({expectedQuantity, quantity}) => (expectedQuantity, quantity)
        | Bulk(_, _) => failwith(__LOC__)
        }

        toEqual((10, 3), expect((expectedQuantity, quantity)))
      },
    )

    let cart = cart->reducer(ProductQuantityUpdated(productIdA, UnitQuantity(-1)))

    test(
      "should not update when trying to update quantity with negative value",
      () => {
        let (expectedQuantity, quantity) = switch cart.products->Utils.getProductByKey(
          productIdentifierA,
        ) {
        | Unit({expectedQuantity, quantity}) => (expectedQuantity, quantity)
        | Bulk(_, _) => failwith(__LOC__)
        }

        toEqual((10, 3), expect((expectedQuantity, quantity)))
      },
    )

    test("should match snapshot", () => toMatchSnapshot(expect(cart.products)))
  })

  describe("BatchProductExpectedQuantityUpdated", () => {
    let cart =
      initialCart->reducer(
        BatchProductExpectedQuantityUpdated([
          (productIdA, UnitQuantity(12)),
          (productIdC, UnitQuantity(24)),
        ]),
      )

    test(
      "should update productInputA quantity w/ new value",
      () => {
        let quantity = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({quantity}) => quantity
        | Bulk(_, _) => failwith(__LOC__)
        }

        toEqual(12, expect(quantity))
      },
    )
    test(
      "should update productInputC quantity w/ new value",
      () => {
        let quantity = switch cart.products->Utils.getProductByKey(productIdentifierC) {
        | Unit({quantity}) => quantity
        | Bulk(_, _) => failwith(__LOC__)
        }

        toEqual(24, expect(quantity))
      },
    )
  })

  describe("ProductUnitPriceUpdated", () => {
    let cart = initialCart->reducer(ProductUnitPriceUpdated(productIdC, 23.))

    test(
      "should update price",
      () => {
        let unitPrice = switch cart.products->Utils.getProductByKey(productIdentifierC) {
        | Unit({unitPrice}) | Bulk({unitPrice}, _) => unitPrice
        }

        toBe(23., expect(unitPrice))
      },
    )

    test("should match snapshot", () => expect(cart.products)->toMatchSnapshot)
  })

  describe("ProductNameUpdated, ProductDescriptionUpdated", () => {
    let newName = "A new name for product C"
    let newDescription = "This is the final description for product C"
    let cart =
      initialCart
      ->reducer(ProductNameUpdated(productIdC, newName))
      ->reducer(ProductDescriptionUpdated(productIdC, newDescription))
    let foundProduct = cart.products->Utils.getProductByKey(productIdentifierC)

    test(
      "should update name",
      () => {
        let name = switch foundProduct {
        | Unit({name}) | Bulk({name}, _) => name
        }

        toBe(newName, expect(name))
      },
    )

    test(
      "should update description",
      () => {
        let description = switch foundProduct {
        | Unit({description}) | Bulk({description}, _) => description
        }

        toBe(newDescription, expect(description))
      },
    )

    test("should match snapshot", () => expect(cart)->toMatchSnapshot)
  })

  describe("ProductFeeAdded", () => {
    test(
      "should add some fees - productInputC has initially a fee inside",
      () => {
        let cart =
          initialCart->reducer(ProductFeeAdded(productIdC))->reducer(ProductFeeAdded(productIdC))
        let fees = switch cart.products->Utils.getProductByKey(productIdentifierC) {
        | Unit({fees}) | Bulk({fees}, _) => fees
        }

        toHaveLength(3, expect(fees))
      },
    )

    test(
      "should not add fees anymore, all feeKinds are taken in productInputA",
      () => {
        let cart =
          initialCart->reducer(ProductFeeAdded(productIdA))->reducer(ProductFeeAdded(productIdA))
        let fees = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({fees}) | Bulk({fees}, _) => fees
        }

        toHaveLength(3, expect(fees))
      },
    )
  })

  describe("ProductFeeRemoved", () => {
    let feeToRemove = switch initialCart.products->Utils.getProductByKey(productIdentifierA) {
    | Unit({fees}) | Bulk({fees}, _) =>
      fees->Array.keep(fee => fee.kind === Transport)->Array.getExn(0)
    }
    let cart = initialCart->reducer(ProductFeeRemoved(productIdA, feeToRemove.id))

    test(
      "should remove a fee from a product and output product fees length",
      () => {
        let fees = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({fees}) | Bulk({fees}, _) => fees
        }

        toBe(2, expect(fees->Array.length))
      },
    )

    test(
      "should remove a fee from a product and match snapshot",
      () => toMatchSnapshot(expect(cart)),
    )
  })

  describe("ProductFeeUpdated", () => {
    let feeToUpdate = switch initialCart.products->Utils.getProductByKey(productIdentifierC) {
    | Unit({fees}) | Bulk({fees}, _) => fees->Array.keep(fee => fee.kind === Taxes)->Array.getExn(0)
    }
    let cart =
      initialCart->reducer(
        ProductFeeUpdated(productIdC, feeToUpdate.id, FeeInputs.transport_0_93->Maker.Fee.make),
      )

    test("should update a fee and match snapshot", () => toMatchSnapshot(expect(cart)))
  })

  describe("ProductFeeReplicated", () => {
    let cart = initialCart->reducer(ProductFeeReplicated(FeeInputs.other_0_01->Maker.Fee.make))

    test(
      "should replicate a fee to all products in cart - productInputC case",
      () => {
        let fees = switch cart.products->Utils.getProductByKey(productIdentifierC) {
        | Unit({fees}) | Bulk({fees}, _) => fees
        }

        toHaveLength(2, expect(fees))
      },
    )

    test(
      "should just update existing fee in product; no adding new fee - productInputA case",
      () => {
        let fees = switch initialCart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({fees}) | Bulk({fees}, _) => fees
        }

        toHaveLength(3, expect(fees))
      },
    )

    test("should match snapshot", () => toMatchSnapshot(expect(cart.products)))
  })

  describe("ProductDiscountAdded", () => {
    test(
      "should match snapshot after applying percent discount",
      () =>
        toMatchSnapshot(
          expect(
            initialCart->reducer(ProductDiscountAdded(productIdC, DiscountInputs.percent_3_33)),
          ),
        ),
    )

    test(
      "should match snapshot after applying currency discount",
      () =>
        toMatchSnapshot(
          expect(
            initialCart->reducer(ProductDiscountAdded(productIdC, DiscountInputs.currency_0_01)),
          ),
        ),
    )
  })

  describe("ProductDiscountRemoved", () => {
    let discountToRemove = switch initialCart.products->Utils.getProductByKey(productIdentifierA) {
    | Unit({discounts}) | Bulk({discounts}, _) =>
      discounts->Array.keep(discount => discount.kind === Percent)->Array.getExn(0)
    }
    let cart = initialCart->reducer(ProductDiscountRemoved(productIdA, discountToRemove.id))

    test(
      "should remove a discount from a product and output product discounts length",
      () => {
        let discounts = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({discounts}) | Bulk({discounts}, _) => discounts
        }

        toBe(0, expect(discounts->Array.length))
      },
    )
    test(
      "should remove a discount from a product and match snapshot",
      () => toMatchSnapshot(expect(cart)),
    )
  })

  describe("ProductDiscountUpdated", () => {
    let capacityPrecision = switch initialCart.products->Utils.getProductByKey(productIdentifierA) {
    | Bulk(_, precision) => Some(precision)
    | Unit(_) => None
    }
    let discountToUpdate = switch initialCart.products->Utils.getProductByKey(productIdentifierA) {
    | Unit({discounts}) | Bulk({discounts}, _) =>
      discounts->Array.keep(discount => discount.kind === Percent)->Array.getExn(0)
    }
    let cart =
      initialCart->reducer(
        ProductDiscountUpdated(
          productIdA,
          discountToUpdate.id,
          DiscountInputs.free_0->Maker.Discount.make(~capacityPrecision?),
        ),
      )

    test("should update a discount and match snapshot", () => toMatchSnapshot(expect(cart)))
  })

  describe("GlobalDiscountAdded", () => {
    let cart = initialCart->reducer(GlobalDiscountAdded(DiscountInputs.percent_3_33))

    test("should apply a global discount to the cart", () => toMatchSnapshot(expect(cart)))
  })

  describe("GlobalDiscountRemoved", () => {
    let discountToRemove =
      initialCart.discounts->Array.keep(discount => discount.kind === Currency)->Array.getExn(0)

    let cart = initialCart->reducer(GlobalDiscountRemoved(discountToRemove.id))

    test(
      "should remove a discount from a cart and output cart discounts length",
      () => toBe(0, expect(cart.discounts->Array.length)),
    )
    test(
      "should remove a discount from a cart and match snapshot",
      () => toMatchSnapshot(expect(cart)),
    )
  })

  describe("GlobalDiscountUpdated", () => {
    let discountToUpdate =
      initialCart.discounts->Array.keep(discount => discount.kind === Currency)->Array.getExn(0)

    let cart =
      initialCart->reducer(
        GlobalDiscountUpdated(discountToUpdate.id, DiscountInputs.percent_20->Maker.Discount.make),
      )

    test(
      "should update a discount in a cart and output cart discounts length",
      () => toBe(1, expect(cart.discounts->Array.length)),
    )
    test(
      "should update a discount in a cart and match snapshot",
      () => toMatchSnapshot(expect(cart)),
    )
    test(
      "should not update any discount in a cart because the discount.id provided doesnt exists",
      () => {
        let cart =
          initialCart->reducer(
            GlobalDiscountUpdated("some_id", DiscountInputs.percent_20->Maker.Discount.make),
          )

        toMatchSnapshot(expect(cart))
      },
    )
  })

  describe("TaxesFreeToggleRequested", () => {
    let cart = initialCart->reducer(TaxesFreeToggleRequested)

    test("should toggle the taxesFree boolean value", () => toBe(true, expect(cart.taxesFree)))
    test("should not pre compute the totalTaxes", () => toEqual(None, expect(cart.totalTaxes)))
    test(
      "should not post compute the formatted totalTaxes",
      () => toEqual(None, expect(cart.formattedTotalTaxes)),
    )
    test(
      "should return an equal amount excluding taxes amount with including taxes amount",
      () => {
        let (
          formattedTotalAmountExcludingTaxes,
          formattedTotalAmountIncludingTaxes,
        ) = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({formattedTotalAmountExcludingTaxes, formattedTotalAmountIncludingTaxes})
        | Bulk({formattedTotalAmountExcludingTaxes, formattedTotalAmountIncludingTaxes}, _) => (
            formattedTotalAmountExcludingTaxes,
            formattedTotalAmountIncludingTaxes,
          )
        }

        toEqual(formattedTotalAmountIncludingTaxes, expect(formattedTotalAmountExcludingTaxes))
      },
    )
    test("should match snapshot", () => toMatchSnapshot(expect(cart)))

    let initialCart = process(initialCart, initialCart)
    let cart = cart->reducer(TaxesFreeToggleRequested)

    test("should compute back taxes", () => toEqual(initialCart, expect(cart)))
  })

  describe("StandardTaxRateUpdated", () => {
    let cart = initialCart->reducer(StandardTaxRateUpdated(5.5))

    test("should update the standardTaxRate field", () => toBe(5.5, expect(cart.standardTaxRate)))
    test(
      "should pre compute the totalTaxes",
      () => toBe(Some(46.02), expect(cart.totalTaxes->Option.map(Big.toFloat))),
    )

    test(
      "should change the totalTaxes for products with a transport fee",
      () => {
        let productAtotalTaxes = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({totalTaxes}) | Bulk({totalTaxes}, _) => totalTaxes
        }
        let productCtotalTaxes = switch cart.products->Utils.getProductByKey(productIdentifierC) {
        | Unit({totalTaxes}) | Bulk({totalTaxes}, _) => totalTaxes
        }

        toEqual(
          (Some(45.8), Some(0.22)),
          expect((
            productAtotalTaxes->Option.map(Big.toFloat),
            productCtotalTaxes->Option.map(Big.toFloat),
          )),
        )
      },
    )
    test("should match snapshot", () => toMatchSnapshot(expect(cart)))

    let initialCart = process(initialCart, initialCart)
    let cart = initialCart->reducer(StandardTaxRateUpdated(20.))

    test(
      "should change the totalTaxes for products with a transport fee",
      () => {
        let productAtotalTaxes = switch cart.products->Utils.getProductByKey(productIdentifierA) {
        | Unit({totalTaxes}) | Bulk({totalTaxes}, _) => totalTaxes
        }
        let productCtotalTaxes = switch cart.products->Utils.getProductByKey(productIdentifierC) {
        | Unit({totalTaxes}) | Bulk({totalTaxes}, _) => totalTaxes
        }

        toEqual(
          (Some(92.21), Some(0.22)),
          expect((
            productAtotalTaxes->Option.map(Big.toFloat),
            productCtotalTaxes->Option.map(Big.toFloat),
          )),
        )
      },
    )

    test("should compute back taxes", () => toEqual(initialCart, expect(cart)))
  })

  describe("process", () => {
    let initialCart =
      {
        products: [productInputA],
        discounts: [DiscountInputs.currency_6_66],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test("should match snapshot", () => toMatchSnapshot(expect(process(initialCart, initialCart))))

    let initialCart =
      {
        products: [productInputA],
        discounts: [DiscountInputs.currency_6_66],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: true,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should match snapshot with taxesFree enabled",
      () => toMatchSnapshot(expect(process(initialCart, initialCart))),
    )
  })
})

describe("process", () => {
  let discountInput: Accounting__Types.discountInput = {
    id: None,
    name: "Remise 10%",
    kind: Percent,
    value: 10.,
    quantity: 0,
  }
  let bulkProductInput: Accounting__Types.productInput = Bulk({
    product: {
      id: None,
      identifier: None,
      stockKeepingUnit: None,
      name: "name",
      description: "description",
      fees: [],
      discounts: [],
      quantity: 1.,
      expectedQuantity: None,
      packaging: Some(1.),
      stock: 5.,
      tax: 20.,
      unitPrice: 1.00,
      capacityUnit: Some("kg"),
    },
    precision: 3,
  })

  describe("with bulk product", () => {
    let cart =
      {
        products: [bulkProductInput],
        discounts: [],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should process cart and match snapshot",
      () => {
        toMatchSnapshot(expect(process(cart, cart)))
      },
    )
  })

  describe("with bulk discounted product", () => {
    let cart =
      {
        products: [
          switch bulkProductInput {
          | Unit({product: bulkProductInput}) =>
            Unit({
              product: {
                ...bulkProductInput,
                discounts: [discountInput],
              },
            })
          | Bulk({product: bulkProductInput, precision}) =>
            Bulk({
              product: {
                ...bulkProductInput,
                discounts: [discountInput],
              },
              precision,
            })
          },
        ],
        discounts: [],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should process cart and match snapshot",
      () => {
        toMatchSnapshot(expect(process(cart, cart)))
      },
    )
  })

  describe("with bulk product and global discount", () => {
    let cart =
      {
        products: [bulkProductInput],
        discounts: [discountInput],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should process cart and match snapshot",
      () => {
        toMatchSnapshot(expect(process(cart, cart)))
      },
    )
  })

  describe("with bulk discounted product and global discount", () => {
    let cart =
      {
        products: [
          switch bulkProductInput {
          | Unit({product: bulkProductInput}) =>
            Unit({
              product: {
                ...bulkProductInput,
                discounts: [discountInput],
              },
            })
          | Bulk({product: bulkProductInput, precision}) =>
            Bulk({
              product: {
                ...bulkProductInput,
                discounts: [discountInput],
              },
              precision,
            })
          },
        ],
        discounts: [discountInput],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should process cart and match snapshot",
      () => {
        toMatchSnapshot(expect(process(cart, cart)))
      },
    )
  })
})
