open Jest

module Formatter = Accounting__Formatter

describe("Formatter", () => {
  open Expect
  open Formatter

  describe("postfixToString", () => {
    test(
      "should correctly convert euro currency postfix to string",
      () => expect(Currency(Eur)->postfixToString) |> toBe(`€`),
    )

    test(
      "should correctly convert usd currency postfix to string",
      () => toBe(`$`, expect(Currency(Usd)->postfixToString)),
    )

    test(
      "should correctly convert pound currency postfix to string",
      () => toBe(`£`, expect(Currency(Pound)->postfixToString)),
    )

    test(
      "should correctly convert percent postfix to string",
      () => toBe("%", expect(Percent->postfixToString)),
    )
  })

  describe("formatAmount", () => {
    test(
      "should correctly format amount with eur currency and precision",
      () => toBe(`12.90 €`, expect(12.9->formatAmount(~precision=2, ~currency=Eur))),
    )

    test(
      "should correctly format amount with usd currency and precision",
      () => toBe(`$12.900`, expect(12.9->formatAmount(~currency=Usd, ~precision=3))),
    )

    test(
      "should correctly format amount with eur currency, precision and bulk unit",
      () =>
        toBe(
          `12.90 €/kg`,
          expect(12.9->formatAmount(~currency=Eur, ~precision=2, ~bulkUnit="kg")),
        ),
    )

    test(
      "should correctly format amount with usd currency with precision and bulk unit",
      () =>
        toBe(`$0.90/kg`, expect(0.9->formatAmount(~currency=Usd, ~precision=2, ~bulkUnit="kg"))),
    )
  })

  describe("formatQuantity", () => {
    test(
      "should correctly format Big amount with bulk unit",
      () => toBe("1000 kg", expect(formatQuantity(1000., ~bulkUnit="kg"))),
    )

    test(
      "should correctly format Big amount with bulk unit and precision",
      () => toBe("1.000 kg", expect(formatQuantity(1., ~precision=3, ~bulkUnit="kg"))),
    )

    test(
      "should correctly format Big amount with precision only",
      () => toBe("10.00", expect(formatQuantity(10., ~precision=2))),
    )

    test("should correctly format Big amount", () => toBe("10", expect(formatQuantity(10.))))
  })
})
