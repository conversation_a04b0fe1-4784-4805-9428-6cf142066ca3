// This is the entry point of the module
// Below are listed the modules and functions we want to externalize
// Calling a module by "Accounting__xxx" is prohibited 👎
module Actions = Accounting__Actions
module Computer = Accounting__Computer
module Formatter = Accounting__Formatter
module Maker = Accounting__Maker
module Reducer = Accounting__Reducer
module Serializer = Accounting__Serializer
module Types = Accounting__Types

type cart = Types.cart
type product = Types.product
type discount = Types.discount
type fee = Types.fee
type tax = Types.tax

open! Accounting__Computer
let compute = make

open! Accounting__Serializer
let serialize = serialize
let deserialize = deserialize

open! Accounting__Formatter.Cart
let format = make

open! Accounting__Formatter
let formatAmount = formatAmount
let formatAmountFromString = formatAmountFromString
let formatAmountFromBig = formatAmountFromBig

open! Accounting__Maker.Cart
let make = make

open! Accounting__Utils
let fromRawProductQuantity = fromRawProductQuantity
let toRawProductQuantity = toRawProductQuantity
let isBulk = isBulk
